import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';

// Initialize Prisma client
const prisma = new PrismaClient();

// System user ID for seeding operations
const SYSTEM_USER_ID = parseInt(process.env.SYSTEM_USER_ID || '1');

/**
 * Utility function to read JSON data files
 */
export const readJsonFile = <T>(fileName: string): T[] => {
  try {
    const filePath = path.join(__dirname, '..', 'data', fileName);
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(fileContent);
  } catch (error) {
    console.error(`Error reading ${fileName}:`, error);
    throw new Error(`Failed to read ${fileName}`);
  }
};

/**
 * Utility function to log progress
 */
export const logProgress = (message: string, current?: number, total?: number) => {
  const timestamp = new Date().toISOString();
  const progressInfo = current && total ? ` (${current}/${total})` : '';
  console.log(`[${timestamp}] ${message}${progressInfo}`);
};

/**
 * Utility function to handle batch operations
 */
export const processBatch = async <T, R>(
  items: T[],
  batchSize: number,
  processor: (batch: T[]) => Promise<R[]>,
  progressMessage: string
): Promise<R[]> => {
  const results: R[] = [];
  const totalBatches = Math.ceil(items.length / batchSize);
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchNumber = Math.floor(i / batchSize) + 1;
    
    logProgress(`${progressMessage} - Batch ${batchNumber}/${totalBatches}`, batchNumber, totalBatches);
    
    try {
      const batchResults = await processor(batch);
      results.push(...batchResults);
    } catch (error) {
      console.error(`Error processing batch ${batchNumber}:`, error);
      throw error;
    }
  }
  
  return results;
};

/**
 * Country data transformation interface
 */
export interface CountryRawData {
  isoCode2?: string;
  isoCode3?: string;
  numericCode?: string;
  name: string;
  phonecode: string;
  flag?: string;
  currency?: string;
  currencySymbol?: string;
  currencyName?: string;
  latitude?: number;
  longitude?: number;
  timezones?: Array<{
    zoneName: string;
    gmtOffset: number;
    gmtOffsetName: string;
    abbreviation: string;
    tzName: string;
  }>;
  status?: string;
}

/**
 * State data transformation interface
 */
export interface StateRawData {
  name: string;
  stateCode?: string;
  latitude?: string | number;
  longitude?: string | number;
  countryCode?: string;
  status?: string;
}

/**
 * City data transformation interface
 */
export interface CityRawData {
  name: string;
  stateCode?: string;
  latitude?: string | number;
  longitude?: string | number;
  status?: string;
}

/**
 * Transform country data to match Prisma schema
 */
export const transformCountryData = (rawData: CountryRawData, index: number): any => {
  // Generate ISO codes if missing
  const isoCode2 = rawData.isoCode2 || generateIsoCode2(rawData.name);
  const isoCode3 = rawData.isoCode3 || generateIsoCode3(rawData.name);
  const numericCode = rawData.numericCode || String(index + 1).padStart(3, '0');

  return {
    isoCode2,
    isoCode3,
    numericCode,
    name: rawData.name,
    phonecode: rawData.phonecode,
    flag: rawData.flag || null,
    currency: rawData.currency || null,
    currencySymbol: rawData.currencySymbol || null,
    currencyName: rawData.currencyName || null,
    latitude: rawData.latitude ? parseFloat(String(rawData.latitude)) : null,
    longitude: rawData.longitude ? parseFloat(String(rawData.longitude)) : null,
    timeZones: rawData.timezones ? JSON.stringify(rawData.timezones) : null,
    createdBy: SYSTEM_USER_ID,
    updatedBy: SYSTEM_USER_ID,
    status: (rawData.status as any) || 'active'
  };
};

/**
 * Transform state data to match Prisma schema
 */
export const transformStateData = (rawData: StateRawData, countryId: number): any => {
  return {
    name: rawData.name,
    stateCode: rawData.stateCode || null,
    countryId,
    latitude: rawData.latitude ? parseFloat(String(rawData.latitude)) : null,
    longitude: rawData.longitude ? parseFloat(String(rawData.longitude)) : null,
    createdBy: SYSTEM_USER_ID,
    updatedBy: SYSTEM_USER_ID,
    status: (rawData.status as any) || 'active'
  };
};

/**
 * Transform city data to match Prisma schema
 */
export const transformCityData = (rawData: CityRawData, stateId: number): any => {
  return {
    name: rawData.name,
    stateId,
    latitude: rawData.latitude ? parseFloat(String(rawData.latitude)) : null,
    longitude: rawData.longitude ? parseFloat(String(rawData.longitude)) : null,
    createdBy: SYSTEM_USER_ID,
    updatedBy: SYSTEM_USER_ID,
    status: (rawData.status as any) || 'active'
  };
};

/**
 * Generate ISO 2-letter code from country name
 */
const generateIsoCode2 = (countryName: string): string => {
  return countryName
    .replace(/[^a-zA-Z\s]/g, '')
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .substring(0, 2)
    .toUpperCase();
};

/**
 * Generate ISO 3-letter code from country name
 */
const generateIsoCode3 = (countryName: string): string => {
  return countryName
    .replace(/[^a-zA-Z\s]/g, '')
    .split(' ')
    .map(word => word.substring(0, 3))
    .join('')
    .substring(0, 3)
    .toUpperCase();
};

/**
 * Create country-code to country-id mapping
 */
export const createCountryCodeMapping = async (): Promise<Map<string, number>> => {
  const countries = await prisma.countries.findMany({
    select: { countryId: true, isoCode2: true, name: true }
  });
  
  const mapping = new Map<string, number>();
  
  countries.forEach(country => {
    // Map by ISO code
    if (country.isoCode2) {
      mapping.set(country.isoCode2, country.countryId);
    }
    // Map by country name (fallback)
    mapping.set(country.name.toLowerCase(), country.countryId);
  });
  
  return mapping;
};

/**
 * Create state-code to state-id mapping
 */
export const createStateCodeMapping = async (): Promise<Map<string, number>> => {
  const states = await prisma.states.findMany({
    select: { stateId: true, stateCode: true, name: true, countryId: true },
    include: { country: { select: { isoCode2: true } } }
  });
  
  const mapping = new Map<string, number>();
  
  states.forEach(state => {
    // Map by state code + country code
    if (state.stateCode && state.country.isoCode2) {
      mapping.set(`${state.stateCode}-${state.country.isoCode2}`, state.stateId);
    }
    // Map by state name + country id (fallback)
    mapping.set(`${state.name.toLowerCase()}-${state.countryId}`, state.stateId);
  });
  
  return mapping;
};

export { prisma, SYSTEM_USER_ID };
