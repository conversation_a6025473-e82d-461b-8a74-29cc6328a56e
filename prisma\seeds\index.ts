import { prisma, logProgress } from './seed-utils';
import { seedCountries } from './countries-seed';
import { seedStates } from './states-seed';
import { seedCities } from './cities-seed';

/**
 * Main seeding function that orchestrates all location data seeding
 */
export const seedLocationData = async (): Promise<void> => {
  const startTime = Date.now();
  
  try {
    logProgress('🚀 Starting complete location data seeding...');
    logProgress('=' .repeat(60));
    
    // Step 1: Seed Countries
    logProgress('Step 1/3: Seeding Countries');
    await seedCountries();
    logProgress('');
    
    // Step 2: Seed States
    logProgress('Step 2/3: Seeding States');
    await seedStates();
    logProgress('');
    
    // Step 3: Seed Cities
    logProgress('Step 3/3: Seeding Cities');
    await seedCities();
    logProgress('');
    
    // Final summary
    const [countryCount, stateCount, cityCount] = await Promise.all([
      prisma.countries.count(),
      prisma.states.count(),
      prisma.cities.count()
    ]);
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    logProgress('=' .repeat(60));
    logProgress('🎉 Location data seeding completed successfully!');
    logProgress(`📊 Summary:`);
    logProgress(`   • Countries: ${countryCount}`);
    logProgress(`   • States: ${stateCount}`);
    logProgress(`   • Cities: ${cityCount}`);
    logProgress(`⏱️  Total time: ${duration} seconds`);
    logProgress('=' .repeat(60));
    
  } catch (error) {
    console.error('❌ Error during location data seeding:', error);
    throw error;
  }
};

/**
 * Reset all location data (use with caution)
 */
export const resetLocationData = async (): Promise<void> => {
  try {
    logProgress('🗑️  Resetting all location data...');
    
    // Delete in reverse order due to foreign key constraints
    await prisma.cities.deleteMany();
    logProgress('✅ Deleted all cities');
    
    await prisma.states.deleteMany();
    logProgress('✅ Deleted all states');
    
    await prisma.countries.deleteMany();
    logProgress('✅ Deleted all countries');
    
    logProgress('🎉 Location data reset completed!');
    
  } catch (error) {
    console.error('❌ Error resetting location data:', error);
    throw error;
  }
};

/**
 * Get seeding statistics
 */
export const getSeedingStats = async (): Promise<{
  countries: number;
  states: number;
  cities: number;
  isSeeded: boolean;
}> => {
  try {
    const [countryCount, stateCount, cityCount] = await Promise.all([
      prisma.countries.count(),
      prisma.states.count(),
      prisma.cities.count()
    ]);
    
    return {
      countries: countryCount,
      states: stateCount,
      cities: cityCount,
      isSeeded: countryCount > 0 && stateCount > 0 && cityCount > 0
    };
  } catch (error) {
    console.error('Error getting seeding stats:', error);
    throw error;
  }
};

/**
 * Main function to run complete seeding
 */
const main = async () => {
  try {
    // Check command line arguments
    const args = process.argv.slice(2);
    
    if (args.includes('--reset')) {
      await resetLocationData();
      logProgress('');
    }
    
    if (args.includes('--stats')) {
      const stats = await getSeedingStats();
      logProgress('📊 Current seeding statistics:');
      logProgress(`   • Countries: ${stats.countries}`);
      logProgress(`   • States: ${stats.states}`);
      logProgress(`   • Cities: ${stats.cities}`);
      logProgress(`   • Is Seeded: ${stats.isSeeded ? 'Yes' : 'No'}`);
      return;
    }
    
    // Run the seeding
    await seedLocationData();
    
  } catch (error) {
    console.error('Failed to complete seeding operation:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
};

// Run if this file is executed directly
if (require.main === module) {
  main();
}

// Export for use in other modules
export default {
  seedLocationData,
  resetLocationData,
  getSeedingStats,
  seedCountries,
  seedStates,
  seedCities
};
