import { 
  prisma, 
  readJsonFile, 
  logProgress, 
  processBatch, 
  transformCityData,
  createStateCodeMapping,
  CityRawData 
} from './seed-utils';

/**
 * Seed cities data
 */
export const seedCities = async (): Promise<void> => {
  try {
    logProgress('🏙️ Starting cities seeding...');
    
    // Check if cities already exist
    const existingCount = await prisma.cities.count();
    if (existingCount > 0) {
      logProgress(`⚠️  Found ${existingCount} existing cities. Skipping cities seeding.`);
      return;
    }
    
    // Ensure states exist
    const stateCount = await prisma.states.count();
    if (stateCount === 0) {
      throw new Error('No states found. Please seed states first.');
    }
    
    // Create state mapping
    logProgress('🗺️  Creating state code mapping...');
    const stateMapping = await createStateCodeMapping();
    logProgress(`📊 Created mapping for ${stateMapping.size} states`);
    
    // Read cities data
    logProgress('📖 Reading cities data...');
    const rawCities = readJsonFile<CityRawData>('cities.json');
    logProgress(`📊 Found ${rawCities.length} cities to process`);
    
    // Transform and validate data
    logProgress('🔄 Transforming cities data...');
    const transformedCities: any[] = [];
    let skippedCities = 0;
    
    for (const city of rawCities) {
      try {
        // Find state ID
        let stateId: number | undefined;
        
        if (city.stateCode) {
          // Try to find state by state code
          for (const [key, id] of stateMapping.entries()) {
            if (key.startsWith(city.stateCode + '-')) {
              stateId = id;
              break;
            }
          }
        }
        
        if (!stateId) {
          // Try to find by city name matching state name (fallback)
          const fallbackStateId = findStateIdFallback(city, stateMapping);
          if (fallbackStateId) {
            stateId = fallbackStateId;
          }
        }
        
        if (!stateId) {
          console.warn(`⚠️  Skipping city ${city.name}: State not found for code ${city.stateCode}`);
          skippedCities++;
          continue;
        }
        
        const transformedCity = transformCityData(city, stateId);
        transformedCities.push(transformedCity);
        
      } catch (error) {
        console.error(`Error transforming city ${city.name}:`, error);
        skippedCities++;
        continue;
      }
    }
    
    logProgress(`📊 Transformed ${transformedCities.length} cities, skipped ${skippedCities} cities`);
    
    if (transformedCities.length === 0) {
      logProgress('⚠️  No valid cities to insert');
      return;
    }
    
    // Batch insert cities
    const BATCH_SIZE = 200;
    logProgress(`💾 Inserting cities in batches of ${BATCH_SIZE}...`);
    
    await processBatch(
      transformedCities,
      BATCH_SIZE,
      async (batch) => {
        return await prisma.$transaction(async (tx) => {
          const results = [];
          for (const city of batch) {
            try {
              const result = await tx.cities.create({
                data: city
              });
              results.push(result);
            } catch (error) {
              console.error(`Error inserting city ${city.name}:`, error);
              // Continue with other cities in the batch
              continue;
            }
          }
          return results;
        });
      },
      'Inserting cities'
    );
    
    // Verify insertion
    const finalCount = await prisma.cities.count();
    logProgress(`✅ Cities seeding completed! Inserted ${finalCount} cities.`);
    
  } catch (error) {
    console.error('❌ Error seeding cities:', error);
    throw error;
  }
};

/**
 * Fallback function to find state ID when direct mapping fails
 */
const findStateIdFallback = (city: CityRawData, stateMapping: Map<string, number>): number | undefined => {
  // Try to match city name with state name (for cases where city and state have same name)
  const cityNameLower = city.name.toLowerCase();
  
  for (const [key, stateId] of stateMapping.entries()) {
    const stateName = key.split('-')[0]; // Extract state name from "statename-countryid" format
    if (stateName === cityNameLower) {
      return stateId;
    }
  }
  
  return undefined;
};

/**
 * Main function to run cities seeding independently
 */
const main = async () => {
  try {
    await seedCities();
  } catch (error) {
    console.error('Failed to seed cities:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
};

// Run if this file is executed directly
if (require.main === module) {
  main();
}
